{"name": "random-useragent", "version": "0.5.0", "description": "Get a random useragent (with an optional filter)", "main": "index.js", "scripts": {"changelog": "github_changelog_generator --user skratchdot --project random-useragent", "clean": "rimraf .nyc_output/ coverage/", "cover": "nyc npm test", "coveralls": "cat coverage/lcov.info | coveralls --verbose", "data": "./scripts/create-data.js", "lint": "eslint *.js scripts/*.js", "prepublish": "npm-run-all lint test", "test": "mocha test.js", "watch": "npm-run-all clean --parallel watch:test watch:lint", "watch:lint": "watch 'npm run lint' .", "watch:test": "npm run test -- -w"}, "author": "skratchdot", "license": "MIT", "bugs": {"url": "https://github.com/skratchdot/random-useragent/issues"}, "homepage": "https://github.com/skratchdot/random-useragent", "repository": {"type": "git", "url": "git://github.com/skratchdot/random-useragent.git"}, "dependencies": {"random-seed": "^0.3.0"}, "devDependencies": {"chai": "^4.2.0", "coveralls": "^3.1.0", "eslint": "^7.6.0", "mocha": "^8.1.1", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "request": "^2.88.2", "rimraf": "^3.0.2", "ua-parser-js": "^0.7.21", "watch": "^1.0.1", "xml2js": "^0.4.23"}, "tonicExampleFilename": ".tonic.example.js", "nyc": {"all": true, "include": ["index.js"], "reporter": ["text", "html", "lcovonly"]}, "keywords": ["random", "useragent", "user-agent", "user", "agent", "data", "web", "developer"]}