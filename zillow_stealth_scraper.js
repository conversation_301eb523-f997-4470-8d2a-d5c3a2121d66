// zillow_stealth_scraper.js
// Advanced Zillow scraper using playwright-extra + stealth plugin to bypass PerimeterX
import { chromium } from 'playwright-extra';
import stealth from 'puppeteer-extra-plugin-stealth';
import randomUA from 'random-useragent';
import * as cheerio from 'cheerio';
import fs from 'fs/promises';

// Apply stealth plugin
chromium.use(stealth());

// Configuration
const PROXY_URL = 'http://customer-MVPLEE_P3m9i-sessid-0123456789-sesstime-10:<PERSON><PERSON>~<EMAIL>:7777';
const TARGET_URL = 'https://www.zillow.com/profile/mccannteam';

// Generate random session ID for unique proxy sessions
function generateSessionId() {
  return Math.floor(Math.random() * 1000000000).toString().padStart(10, '0');
}

// Get dynamic proxy URL with unique session
function getProxyUrl() {
  const sessionId = generateSessionId();
  return `http://customer-MVPLEE_P3m9i-sessid-${sessionId}-sesstime-10:<PERSON><PERSON>~<EMAIL>:7777`;
}

// Human-like delays
function randomDelay(min = 2000, max = 5000) {
  return min + Math.random() * (max - min);
}

// Simulate human mouse behavior
async function simulateHumanBehavior(page) {
  // Random mouse movements
  await page.mouse.move(
    Math.random() * 800,
    Math.random() * 600,
    { steps: 10 + Math.random() * 20 }
  );

  await page.waitForTimeout(randomDelay(500, 1500));

  // Human-like scrolling
  await page.evaluate(() => {
    window.scrollTo({
      top: Math.random() * 800,
      behavior: 'smooth'
    });
  });

  await page.waitForTimeout(randomDelay(1000, 3000));
}

// Check for PerimeterX blocking
async function isBlocked(page) {
  const pxIndicators = [
    'iframe[src*="px-captcha"]',
    'script[src*="perimeterx"]',
    'script[src*="px-cloud"]',
    '[data-px-name]',
    'text=Access to this page has been denied',
    'text=Please verify you are a human'
  ];

  for (const indicator of pxIndicators) {
    if (await page.$(indicator)) {
      return true;
    }
  }

  // Check page title
  const title = await page.title();
  if (title.includes('Access to this page has been denied') ||
      title.includes('blocked') ||
      title.includes('captcha')) {
    return true;
  }

  return false;
}

// Enhanced data extraction with Cheerio (keeping your existing logic)
function extractDataWithCheerio(html, pageUrl) {
  const $ = cheerio.load(html);

  // Helper functions
  const getText = (selector) => {
    const element = $(selector);
    return element.length > 0 ? element.text().trim() : '';
  };

  const getAttr = (selector, attr) => {
    const element = $(selector);
    return element.length > 0 ? element.attr(attr) || '' : '';
  };

  const extractPhone = (text) => {
    const phoneMatch = text.match(/\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    return phoneMatch ? phoneMatch[0].replace(/\D/g, '') : '';
  };

  // Extract basic profile information
  const name = getText('h1') || getText('[data-testid="agent-name"]');

  // Extract contact information
  const phoneText = getText('a[href^="tel:"]') || getText('[data-testid="agent-phone"]');
  const phone = extractPhone(phoneText);

  // Extract business information
  const businessName = getText('[data-testid="agent-company"]') ||
                      getText('.brokerage-name') ||
                      getText('.company-name');

  // Extract profile photo
  const profilePhoto = getAttr('img[alt*="Profile"]', 'src') ||
                      getAttr('[data-testid="agent-photo"] img', 'src');

  // Extract ratings and reviews
  const ratingText = getText('[data-testid="agent-rating"]') || getText('.rating');
  const reviewCountText = getText('[data-testid="review-count"]') || getText('.review-count');

  // Extract listings information
  const forSaleListings = [];
  const forRentListings = [];

  // Look for property cards
  $('article[data-testid*="property"], [data-testid="property-card"], .property-card, .listing-card, a[href*="/homedetails/"]').each((i, element) => {
    const $card = $(element).closest('article, div, li');
    if ($card.length === 0) return;

    const cardText = $card.text();
    const cardHtml = $card.html();

    // Extract ZPID
    const zpidMatch = cardHtml.match(/\/(\d+)_zpid/) || cardHtml.match(/zpid[=:](\d+)/);
    const zpid = zpidMatch ? zpidMatch[1] : null;

    // Extract address
    const address = getText($card.find('[data-testid*="address"], .address, .property-address')) ||
                   cardText.match(/\d+\s+[A-Za-z\s]+(?:St|Ave|Rd|Dr|Ln|Blvd|Way|Ct|Pl)/)?.[0] || '';

    // Extract price
    const priceMatch = cardText.match(/\$[\d,]+/) || cardText.match(/\$\d+[KM]?/);
    const price = priceMatch ? priceMatch[0] : '';

    // Extract bed/bath info
    const bedMatch = cardText.match(/(\d+)\s*(?:bed|bd|bedroom)/i);
    const bathMatch = cardText.match(/(\d+(?:\.\d+)?)\s*(?:bath|ba|bathroom)/i);

    const listing = {
      zpid: zpid,
      home_type: getText($card.find('[data-testid*="type"], .property-type')) ||
                (cardText.includes('Condo') ? 'Condo' :
                 cardText.includes('Townhouse') ? 'Townhouse' :
                 cardText.includes('Single Family') ? 'Single Family' : null),
      address: address,
      bedrooms: bedMatch ? Number.parseInt(bedMatch[1]) : null,
      bathrooms: bathMatch ? Number.parseFloat(bathMatch[1]) : null,
      price: price,
      latitude: null,
      longitude: null,
      brokerage_name: businessName,
      home_marketing_status: getText($card.find('[data-testid*="status"], .status')) ||
                            (cardText.includes('For Sale') ? 'For Sale' :
                             cardText.includes('For Rent') ? 'For Rent' :
                             cardText.includes('Sold') ? 'Sold' : null),
      listing_url: getAttr($card.find('a[href*="/homedetails/"], a[href*="/b/"]'), 'href') || ''
    };

    // Only add if we have meaningful data
    if (listing.zpid || listing.address || listing.price) {
      const cardTextLower = cardText.toLowerCase();
      if (cardTextLower.includes('for rent') || cardTextLower.includes('rental') || cardTextLower.includes('/rent/')) {
        forRentListings.push(listing);
      } else {
        forSaleListings.push(listing);
      }
    }
  });

  // Extract sales statistics
  const salesStatsText = $('body').text();
  console.log('Full page text for analysis:', `${salesStatsText.substring(0, 1000)}...`);

  const salesCountMatch = salesStatsText.match(/(\d+)\s+(?:sales?|transactions?|deals?)/i) ||
                         salesStatsText.match(/sold\s+(\d+)/i) ||
                         salesStatsText.match(/(\d+)\s+(?:homes?|properties?)\s+sold/i);

  const priceRangeMatch = salesStatsText.match(/\$([0-9,]+(?:\.[0-9]+)?[KMB]?).*?\$([0-9,]+(?:\.[0-9]+)?[KMB]?)/i) ||
                         salesStatsText.match(/(?:from|between)\s*\$([0-9,]+).*?(?:to|and)\s*\$([0-9,]+)/i);

  // Extract email addresses
  const emailMatches = salesStatsText.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g) || [];
  const emailFromHref = getAttr('a[href^="mailto:"]', 'href')?.replace('mailto:', '') || null;
  const extractedEmail = emailFromHref || emailMatches[0] || null;

  // Extract reviews
  const reviews = [];
  $('[data-testid="review"], .review, .review-item, [class*="review"]').each((i, element) => {
    const $review = $(element);
    const reviewText = getText($review);
    if (reviewText.length > 10) {
      reviews.push({
        reviewComment: getText($review.find('[data-testid="review-comment"], .review-text, .review-content')) || reviewText,
        reviewId: getAttr($review, 'data-review-id') || `review_${i}`,
        rating: getText($review.find('[data-testid="review-rating"], .rating, .stars')).match(/\d+/)?.[0] || null,
        createDate: getText($review.find('[data-testid="review-date"], .review-date, .date'))
      });
    }
  });

  // Extract team information
  const teamMembers = [];
  $('[data-testid="team-member"], .team-member').each((i, element) => {
    const $member = $(element);
    teamMembers.push({
      name: getText($member.find('[data-testid="member-name"], .member-name')),
      role: getText($member.find('[data-testid="member-role"], .member-role')),
      photo: getAttr($member.find('img'), 'src')
    });
  });

  return {
    // Basic profile information
    url: pageUrl,
    encodedZuid: null,
    screenName: name,
    inCanada: false,
    name: name,
    flag: null,
    profileTypeIds: [],
    profileTypes: [],
    sidebarVideoUrl: getAttr('video', 'src'),

    // Business information
    businessAddress: {
      address1: null,
      address2: null,
      city: null,
      state: null,
      postalCode: null
    },
    businessName: businessName,
    cpdUserPronouns: null,

    // Agent status
    isTopAgent: $('body').text().toLowerCase().includes('top agent'),
    isPremierAgent: $('body').text().toLowerCase().includes('premier agent'),

    // Profile media
    profileImageId: null,
    profilePhotoSrc: profilePhoto,

    // Ratings and reviews
    ratings: {
      totalCount: Number.parseInt(reviewCountText.match(/\d+/)?.[0] || '0'),
      averageRating: Number.parseFloat(ratingText.match(/[\d.]+/)?.[0] || '0')
    },

    // Contact information
    phoneNumbers: {
      cell: null,
      brokerage: null,
      business: phone
    },
    email: extractedEmail,

    // Professional information
    professional: {},
    getToKnowMe: {
      description: getText('[data-testid="agent-bio"], .agent-bio, .description')
    },
    agentLicenses: [],

    // Sales statistics
    agentSalesStats: {
      countAllTime: Number.parseInt(salesCountMatch?.[1] || '0'),
      countLastYear: 0,
      priceRangeThreeYearMin: priceRangeMatch ? Number.parseInt(priceRangeMatch[1].replace(/,/g, '')) : null,
      priceRangeThreeYearMax: priceRangeMatch ? Number.parseInt(priceRangeMatch[2].replace(/,/g, '')) : null,
      averageValueThreeYear: null,
      stats_include_team: $('body').text().toLowerCase().includes('team')
    },

    // Listings data
    forSaleListings: forSaleListings,
    forRentListings: forRentListings,

    // Past sales
    pastSales: {
      totalSales: Number.parseInt(salesCountMatch?.[1] || '0'),
      past_sales: []
    },

    // Additional data
    preferredLenders: {},
    professionalInformation: [],

    // Reviews data
    reviewsData: {
      reviews: reviews,
      subRatings: {},
      reviewee: {
        name: name
      }
    },

    // Team information
    teamDisplayInformation: {
      teamLeadInfo: teamMembers.length > 0 ? teamMembers[0] : {},
      teamMemberInfo: teamMembers
    },

    // Metadata
    scrapedAt: new Date().toISOString(),
    pageTitle: $('title').text(),

    // Debug info
    _debug: {
      extractionMethod: 'playwright-extra-stealth',
      elementsFound: {
        propertyCards: $('[data-testid="property-card"], .property-card').length,
        reviews: $('[data-testid="review"], .review').length,
        teamMembers: $('[data-testid="team-member"], .team-member').length
      }
    }
  };
}

// Main scraping function with stealth approach
async function scrapeZillowStealth(targetUrl = TARGET_URL) {
  const proxyUrl = getProxyUrl();
  const userAgent = randomUA.getRandom();

  console.log(`🚀 Starting stealth scrape...`);
  console.log(`📍 Target: ${targetUrl}`);
  console.log(`🔒 Proxy: ${proxyUrl.split('@')[0]}@***`);
  console.log(`🤖 User Agent: ${userAgent.substring(0, 50)}...`);

  const browser = await chromium.launch({
    headless: false, // Use visible browser for better stealth
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-blink-features=AutomationControlled',
      '--disable-features=VizDisplayCompositor'
    ]
  });

  // Parse proxy URL for context configuration
  const proxyParts = proxyUrl.match(/http:\/\/([^:]+):([^@]+)@([^:]+):(\d+)/);
  const [, username, password, host, port] = proxyParts;

  const context = await browser.newContext({
    userAgent: userAgent,
    locale: 'en-US',
    timezoneId: 'America/New_York',
    viewport: {
      width: 1200 + Math.floor(Math.random() * 400),
      height: 700 + Math.floor(Math.random() * 300)
    },
    permissions: ['geolocation'],
    geolocation: { latitude: 40.7128, longitude: -74.0060 },
    colorScheme: 'light',
    proxy: {
      server: `http://${host}:${port}`,
      username: username,
      password: password
    }
  });

  const page = await context.newPage();

  try {
    // Step 1: Warm-up navigation to build session trust
    const homeUrl = new URL(targetUrl).origin;
    console.log(`🏠 Warming up session at ${homeUrl}...`);

    await page.goto(homeUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    // Check if blocked on homepage
    if (await isBlocked(page)) {
      console.error('❌ Blocked on homepage - proxy may be flagged');
      await browser.close();
      return null;
    }

    console.log('✅ Homepage loaded successfully');

    // Simulate human behavior on homepage
    await simulateHumanBehavior(page);
    await page.waitForTimeout(randomDelay(3000, 6000));

    // Step 2: Navigate to target page
    console.log(`🎯 Navigating to target page...`);

    const response = await page.goto(targetUrl, {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    console.log(`📊 Response status: ${response.status()}`);

    // Step 3: Check for PerimeterX blocking
    if (await isBlocked(page)) {
      console.error('❌ PerimeterX challenge detected - need to rotate proxy');
      await browser.close();
      return null;
    }

    console.log('✅ Target page loaded without blocking');

    // Step 4: Additional human behavior simulation
    await simulateHumanBehavior(page);
    await page.waitForTimeout(randomDelay(2000, 4000));

    // Step 5: Extract data
    console.log('📄 Extracting HTML content...');
    const html = await page.content();

    console.log('🔍 Parsing data with Cheerio...');
    const data = extractDataWithCheerio(html, targetUrl);

    // Step 6: Save results
    const timestamp = Date.now();
    const filename = `zillow_stealth_${timestamp}.json`;

    await fs.writeFile(filename, JSON.stringify(data, null, 2));
    console.log(`💾 Data saved to ${filename}`);

    // Log extraction summary
    console.log('📈 Extraction Summary:');
    console.log(`   Name: ${data.name || 'Not found'}`);
    console.log(`   Business: ${data.businessName || 'Not found'}`);
    console.log(`   Phone: ${data.phoneNumbers.business || 'Not found'}`);
    console.log(`   Email: ${data.email || 'Not found'}`);
    console.log(`   For Sale Listings: ${data.forSaleListings.length}`);
    console.log(`   Reviews: ${data.reviewsData.reviews.length}`);
    console.log(`   Sales Count: ${data.agentSalesStats.countAllTime}`);

    await browser.close();
    return data;

  } catch (error) {
    console.error('💥 Error during scraping:', error.message);
    await browser.close();
    return null;
  }
}

// Run the scraper
console.log('🎬 Starting Zillow Stealth Scraper...');
scrapeZillowStealth()
  .then(result => {
    if (result) {
      console.log('🎉 Scraping completed successfully!');
    } else {
      console.log('😞 Scraping failed - check logs above');
    }
  })
  .catch(error => {
    console.error('💀 Fatal error:', error);
    process.exit(1);
  });

export { scrapeZillowStealth, extractDataWithCheerio };
