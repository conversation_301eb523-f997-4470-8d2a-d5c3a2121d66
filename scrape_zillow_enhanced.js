// scrape_zillow_enhanced.js
// Enhanced <PERSON>illow scraper based on successful Cheerio approach
// Incorporates lessons learned from the winning scraper

import playwright from 'playwright';
import * as cheerio from 'cheerio';
import { config } from './scraper-config.js';

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

async function solveHumanChallenge(page, log = console) {
  for (let attempt = 0; attempt < 5; attempt++) {
    await page.waitForTimeout(1000 + Math.random() * 2000);

    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true;
    }

    const box = await btn.boundingBox();
    if (!box) {
      log.warn('Challenge button found but no bounding box');
      continue;
    }

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);

    await page.mouse.move(
      cx + (Math.random() - 0.5) * 20,
      cy + (Math.random() - 0.5) * 20,
      { steps: 15 + Math.floor(Math.random() * 10) }
    );

    await page.waitForTimeout(500 + Math.random() * 1000);
    await page.mouse.move(cx, cy, { steps: 5 });
    await page.waitForTimeout(200 + Math.random() * 300);

    await page.mouse.down();
    log.info('Holding button...');

    const holdTime = 6000 + Math.random() * 3000;
    const start = Date.now();
    let moveCount = 0;

    while (Date.now() - start < holdTime) {
      if (moveCount % 10 === 0) {
        await page.mouse.move(
          cx + (Math.random() - 0.5) * 2,
          cy + (Math.random() - 0.5) * 2,
          { steps: 1 }
        );
      }
      moveCount++;
      await page.waitForTimeout(150 + Math.random() * 100);
    }

    await page.mouse.up();
    log.info('Released button, waiting for verification...');

    await page.waitForTimeout(3000 + Math.random() * 2000);

    try {
      await Promise.race([
        page.waitForSelector('text=Press and Hold', { state: 'detached', timeout: 5000 }),
        page.waitForLoadState('networkidle', { timeout: 5000 }),
        page.waitForTimeout(5000)
      ]);
    } catch (e) {
      log.info('Timeout waiting for challenge resolution');
    }

    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }

    log.warn(`❌ Challenge still present after attempt ${attempt + 1}, retrying...`);
    await page.waitForTimeout(3000 + Math.random() * 2000);

    if (attempt >= 2) {
      log.info('Refreshing page to get new challenge...');
      await page.reload({ waitUntil: 'networkidle' });
      await page.waitForTimeout(3000);
    }
  }

  log.error('Failed to solve human challenge after 5 attempts');
  return false;
}

function extractDataWithCheerio(html, pageUrl) {
  const $ = cheerio.load(html);

  // Helper function to safely extract text
  const getText = (selector) => {
    const element = $(selector);
    return element.length > 0 ? element.text().trim() : '';
  };

  // Helper function to get attribute
  const getAttr = (selector, attr) => {
    const element = $(selector);
    return element.length > 0 ? element.attr(attr) || '' : '';
  };

  // Helper function to extract phone number
  const extractPhone = (text) => {
    const phoneMatch = text.match(/\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    return phoneMatch ? phoneMatch[0].replace(/\D/g, '') : '';
  };

  // Get full page text for comprehensive analysis
  const fullPageText = $('body').text();
  console.log('🔍 Analyzing page content...');
  console.log(`Page text length: ${fullPageText.length} characters`);

  // Extract basic profile information with multiple selectors
  const name = getText('h1') ||
               getText('[data-testid="agent-name"]') ||
               getText('.agent-name') ||
               getText('.profile-name');

  console.log(`👤 Found agent name: "${name}"`);

  // Enhanced contact information extraction
  const phoneText = getText('a[href^="tel:"]') ||
                   getText('[data-testid="agent-phone"]') ||
                   getText('.phone') ||
                   extractPhone(fullPageText);
  const phone = extractPhone(phoneText);
  console.log(`📞 Found phone: "${phone}"`);

  // Enhanced email extraction with multiple methods
  const emailFromHref = getAttr('a[href^="mailto:"]', 'href')?.replace('mailto:', '') || null;
  const emailMatches = fullPageText.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g) || [];
  const extractedEmail = emailFromHref || emailMatches[0] || null;
  console.log(`📧 Found email: "${extractedEmail}"`);

  // Enhanced agent status detection
  const pageTextLower = fullPageText.toLowerCase();
  const isTopAgent = pageTextLower.includes('top agent') ||
                    pageTextLower.includes('top-rated') ||
                    pageTextLower.includes('top performing');
  const isPremierAgent = pageTextLower.includes('premier agent') ||
                        pageTextLower.includes('zillow premier');

  console.log(`🏆 Agent status - Top: ${isTopAgent}, Premier: ${isPremierAgent}`);

  // Enhanced sales statistics extraction
  const salesPatterns = [
    /(\d+)\s+(?:sales?|transactions?|deals?)/i,
    /sold\s+(\d+)/i,
    /(\d+)\s+(?:homes?|properties?)\s+sold/i,
    /(\d+)\s+(?:closed|completed)\s+(?:sales?|transactions?)/i
  ];

  let salesCount = 0;
  for (const pattern of salesPatterns) {
    const match = fullPageText.match(pattern);
    if (match) {
      salesCount = Number.parseInt(match[1]);
      console.log(`📊 Found sales count: ${salesCount} (pattern: ${pattern})`);
      break;
    }
  }

  // Enhanced price range extraction
  const pricePatterns = [
    /\$([0-9,]+(?:\.[0-9]+)?[KMB]?).*?\$([0-9,]+(?:\.[0-9]+)?[KMB]?)/i,
    /(?:from|between)\s*\$([0-9,]+).*?(?:to|and)\s*\$([0-9,]+)/i,
    /price\s+range[:\s]+\$([0-9,]+).*?\$([0-9,]+)/i
  ];

  let priceMin = null;
  let priceMax = null;
  for (const pattern of pricePatterns) {
    const match = fullPageText.match(pattern);
    if (match) {
      priceMin = Number.parseInt(match[1].replace(/[,KMB]/g, ''));
      priceMax = Number.parseInt(match[2].replace(/[,KMB]/g, ''));
      console.log(`💰 Found price range: $${priceMin} - $${priceMax}`);
      break;
    }
  }

  return {
    // Basic profile information
    url: pageUrl,
    encodedZuid: null,
    screenName: name,
    inCanada: false,
    name: name,
    flag: null,
    profileTypeIds: [],
    profileTypes: [],
    sidebarVideoUrl: getAttr('video', 'src'),

    // Business information
    businessAddress: {
      address1: null,
      address2: null,
      city: null,
      state: null,
      postalCode: null
    },
    businessName: getText('[data-testid="agent-company"]') || getText('.brokerage-name'),
    cpdUserPronouns: null,

    // Agent status (ENHANCED - this was key to success)
    isTopAgent: isTopAgent,
    isPremierAgent: isPremierAgent,

    // Profile media
    profileImageId: null,
    profilePhotoSrc: getAttr('img[alt*="Profile"]', 'src'),

    // Ratings and reviews
    ratings: {
      totalCount: 0,
      averageRating: 0
    },

    // Contact information (ENHANCED - this was key to success)
    phoneNumbers: {
      cell: null,
      brokerage: null,
      business: phone
    },
    email: extractedEmail,

    // Professional information
    professional: {},
    getToKnowMe: {
      description: getText('[data-testid="agent-bio"]')
    },
    agentLicenses: [],

    // Sales statistics (ENHANCED - this was key to success)
    agentSalesStats: {
      countAllTime: salesCount,
      countLastYear: 0,
      priceRangeThreeYearMin: priceMin,
      priceRangeThreeYearMax: priceMax,
      averageValueThreeYear: null,
      stats_include_team: pageTextLower.includes('team')
    },

    // Listings data
    forSaleListings: [],
    forRentListings: [],

    // Past sales
    pastSales: {
      totalSales: salesCount,
      past_sales: []
    },

    // Additional data
    preferredLenders: {},
    professionalInformation: [],

    // Reviews data
    reviewsData: {
      reviews: [],
      subRatings: {},
      reviewee: {
        name: name
      }
    },

    // Team information
    teamDisplayInformation: {
      teamLeadInfo: {},
      teamMemberInfo: []
    },

    // Metadata
    scrapedAt: new Date().toISOString(),
    pageTitle: $('title').text(),

    // Debug info
    _debug: {
      extractionMethod: 'enhanced-cheerio',
      pageTextLength: fullPageText.length,
      emailSources: {
        fromHref: emailFromHref,
        fromText: emailMatches
      },
      salesDetection: {
        count: salesCount,
        priceRange: { min: priceMin, max: priceMax }
      }
    }
  };
}

async function scrapeZillowEnhanced(url) {
  console.log('🚀 Starting enhanced Zillow scraper...');

  const browser = await playwright.chromium.launch({
    headless: false,
    args: config.browserArgs
  });

  const context = await browser.newContext({
    userAgent: getRandomUserAgent(),
    viewport: {
      width: 1366 + Math.floor(Math.random() * 200),
      height: 768 + Math.floor(Math.random() * 200)
    },
    locale: 'en-US',
    timezoneId: 'America/New_York',
    permissions: ['geolocation'],
    geolocation: { latitude: 40.7128, longitude: -74.0060 },
    colorScheme: 'light'
  });

  const page = await context.newPage();

  try {
    // Apply stealth techniques
    await page.addInitScript(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      window.chrome = {
        runtime: {},
        loadTimes: () => {},
        csi: () => {},
        app: {}
      };

      Object.defineProperty(navigator, 'plugins', {
        get: () => ({
          length: 3,
          0: { name: 'Chrome PDF Plugin' },
          1: { name: 'Chrome PDF Viewer' },
          2: { name: 'Native Client' }
        }),
      });
    });

    // Set headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
      'Referer': getRandomReferer()
    });

    console.log(`🎯 Navigating directly to target URL: ${url}...`);

    const response = await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    console.log(`📡 Response status: ${response.status()}`);

    await page.waitForTimeout(3000);

    const challengeSolved = await solveHumanChallenge(page);
    if (!challengeSolved) {
      console.error('❌ Failed to solve human challenge');
      return null;
    }

    try {
      await page.waitForLoadState('networkidle', { timeout: 30000 });
    } catch (e) {
      console.log('⏰ Page did not reach networkidle state, continuing...');
    }

    // Get the HTML content for Cheerio
    console.log('📄 Extracting HTML content for Cheerio parsing...');
    const html = await page.content();

    // Extract data using enhanced Cheerio
    console.log('🔍 Parsing data with enhanced Cheerio...');
    const data = extractDataWithCheerio(html, url);

    console.log('✅ Data extraction completed!');
    console.log('📊 Summary:');
    console.log(`   - Name: ${data.name}`);
    console.log(`   - Email: ${data.email}`);
    console.log(`   - Phone: ${data.phoneNumbers.business}`);
    console.log(`   - Top Agent: ${data.isTopAgent}`);
    console.log(`   - Premier Agent: ${data.isPremierAgent}`);
    console.log(`   - Sales Count: ${data.agentSalesStats.countAllTime}`);
    console.log(`   - Price Range: $${data.agentSalesStats.priceRangeThreeYearMin} - $${data.agentSalesStats.priceRangeThreeYearMax}`);

    // Save to file
    const fs = await import('node:fs');
    const filename = `zillow_profile_enhanced_${Date.now()}.json`;
    await fs.promises.writeFile(filename, JSON.stringify(data, null, 2));
    console.log(`💾 Data saved to ${filename}`);

    return data;

  } catch (error) {
    console.error('❌ Error during scraping:', error);
    return null;
  } finally {
    console.log('🔄 Keeping browser open for 10 seconds...');
    await page.waitForTimeout(10000);
    await browser.close();
  }
}

// Run the enhanced scraper
console.log('🎬 Starting enhanced Zillow scraper test...');
await scrapeZillowEnhanced('https://www.zillow.com/profile/mccannteam');

export { extractDataWithCheerio, solveHumanChallenge, scrapeZillowEnhanced };
